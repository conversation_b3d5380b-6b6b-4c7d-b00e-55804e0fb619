<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Gallery</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .image-picker-item {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            border-radius: 8px;
            overflow: hidden;
            background: #f8f9fa;
            position: relative;
        }

        .image-picker-item:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .image-picker-item.selected {
            border-color: #0d6efd;
            background-color: #e7f3ff;
            box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
        }

        .image-picker-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            transition: opacity 0.3s ease;
        }

        #imagesGrid {
            transition: opacity 0.3s ease;
        }

        #imagesLoading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }

        .modal-body {
            position: relative;
            min-height: 300px;
        }

        .image-picker-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            z-index: 1;
        }

        .image-picker-item img[style*="opacity: 1"] + div,
        .image-picker-item img[style*="opacity: 1"] {
            z-index: 2;
            position: relative;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">ทดสอบระบบ Gallery ที่ปรับปรุงแล้ว</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>ทดสอบ Image Picker</h5>
                    </div>
                    <div class="card-body">
                        <div class="image-picker-container">
                            <div class="btn-group" role="group">
                                <button type="button" 
                                        class="btn btn-outline-primary" 
                                        onclick="openImagePicker('test_field', 'general', false)">
                                    <i class="fas fa-images me-2"></i>เลือกจาก Gallery
                                </button>
                                <button type="button" 
                                        class="btn btn-outline-secondary" 
                                        onclick="alert('ฟีเจอร์อัปโหลดใหม่')">
                                    <i class="fas fa-upload me-2"></i>อัปโหลดใหม่
                                </button>
                            </div>
                            <input type="hidden" name="test_field" id="test_field_hidden">
                        </div>
                        
                        <div id="selectedImagePreview" class="mt-3" style="display: none;">
                            <h6>รูปภาพที่เลือก:</h6>
                            <img id="previewImage" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>สถานะการทดสอบ</h5>
                    </div>
                    <div class="card-body">
                        <div id="testStatus">
                            <p><i class="fas fa-clock text-warning"></i> รอการทดสอบ...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Picker Modal -->
    <div class="modal fade" id="imagePickerModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">เลือกรูปภาพ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <input type="text" 
                                   id="imageSearch" 
                                   class="form-control" 
                                   placeholder="ค้นหารูปภาพ...">
                        </div>
                        <div class="col-md-4">
                            <select id="categoryFilter" class="form-select">
                                <option value="">ทุกหมวดหมู่</option>
                                <option value="general">ทั่วไป</option>
                                <option value="menu">เมนูอาหาร</option>
                                <option value="news">ข่าวสาร</option>
                                <option value="hero">หน้าแรก/สไลด์</option>
                                <option value="restaurant">ข้อมูลร้าน</option>
                                <option value="about">เกี่ยวกับเรา</option>
                                <option value="contact">ติดต่อเรา</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-primary w-100" onclick="searchImages()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Images Container -->
                    <div class="position-relative">
                        <!-- Images Grid -->
                        <div id="imagesGrid" class="row g-3" style="min-height: 300px;">
                            <!-- Images will be loaded here -->
                        </div>

                        <!-- Loading -->
                        <div id="imagesLoading" class="text-center py-4" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">กำลังโหลด...</span>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">กำลังโหลดรูปภาพ...</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <button type="button" class="btn btn-primary" onclick="selectImage()">เลือก</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPickerField = null;
        let currentPickerMultiple = false;
        let selectedImages = [];
        let searchTimeout;

        function openImagePicker(fieldName, category = 'general', multiple = false) {
            currentPickerField = fieldName;
            currentPickerMultiple = multiple;
            selectedImages = [];
            
            // Reset modal state
            const grid = document.getElementById('imagesGrid');
            const loading = document.getElementById('imagesLoading');
            const searchInput = document.getElementById('imageSearch');
            
            // Clear previous state
            grid.innerHTML = '';
            grid.style.opacity = '1';
            grid.style.pointerEvents = 'auto';
            loading.style.display = 'none';
            searchInput.value = '';
            
            // Set category filter
            document.getElementById('categoryFilter').value = category;
            
            // Show modal first
            const modal = new bootstrap.Modal(document.getElementById('imagePickerModal'));
            modal.show();
            
            // Update test status
            updateTestStatus('เปิด Gallery Modal แล้ว', 'info');
            
            // Load images after modal is shown
            setTimeout(() => {
                loadImages();
            }, 100);
        }

        function loadImages() {
            const loading = document.getElementById('imagesLoading');
            const grid = document.getElementById('imagesGrid');
            
            updateTestStatus('กำลังโหลดรูปภาพ...', 'warning');
            
            // แสดง loading และซ่อน grid ชั่วคราว
            loading.style.display = 'block';
            grid.style.opacity = '0.3';
            grid.style.pointerEvents = 'none';
            
            const category = document.getElementById('categoryFilter').value;
            const search = document.getElementById('imageSearch').value;
            
            const params = new URLSearchParams();
            if (category) params.append('category', category);
            if (search) params.append('search', search);
            
            // สำหรับการทดสอบ ใช้ข้อมูล mock
            setTimeout(() => {
                loading.style.display = 'none';
                grid.style.opacity = '1';
                grid.style.pointerEvents = 'auto';
                
                // Mock data สำหรับทดสอบ
                const mockImages = [
                    {
                        id: 1,
                        title: 'ก๋วยเตี๋ยวเรือ',
                        original_filename: 'noodle1.jpg',
                        path: 'menu-items/noodle1.jpg',
                        full_url: 'http://localhost:8000/storage/menu-items/noodle1.jpg',
                        formatted_size: '150 KB'
                    },
                    {
                        id: 2,
                        title: 'ก๋วยเตี๋ยวน้ำใส',
                        original_filename: 'noodle2.jpg',
                        path: 'menu-items/noodle2.jpg',
                        full_url: 'http://localhost:8000/storage/menu-items/noodle2.jpg',
                        formatted_size: '200 KB'
                    }
                ];
                
                renderImages(mockImages);
                updateTestStatus('โหลดรูปภาพเสร็จแล้ว (ใช้ข้อมูล Mock)', 'success');
            }, 1000);
        }

        function renderImages(images) {
            const grid = document.getElementById('imagesGrid');
            
            // ใช้ DocumentFragment เพื่อลดการ reflow
            const fragment = document.createDocumentFragment();
            
            if (images.length === 0) {
                const emptyCol = document.createElement('div');
                emptyCol.className = 'col-12 text-center py-4';
                emptyCol.innerHTML = '<p class="text-muted">ไม่พบรูปภาพ</p>';
                fragment.appendChild(emptyCol);
            } else {
                images.forEach(image => {
                    const col = document.createElement('div');
                    col.className = 'col-md-3 col-sm-4 col-6';
                    
                    // สร้าง image element แยกเพื่อจัดการ loading
                    const imageDiv = document.createElement('div');
                    imageDiv.className = 'image-picker-item';
                    imageDiv.onclick = () => toggleImageSelection(image.id, image.full_url, image.path);
                    
                    const img = document.createElement('img');
                    img.alt = image.title || image.original_filename;
                    img.style.opacity = '0';
                    img.style.transition = 'opacity 0.3s ease';
                    
                    // จัดการ loading ของรูปภาพ
                    img.onload = function() {
                        this.style.opacity = '1';
                    };
                    
                    img.onerror = function() {
                        this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuC4hOC4suC4meC5guC4q+C4leC4lOC4o+C4ueC4m+C4oOC4suC4nTwvdGV4dD48L3N2Zz4=';
                        this.style.opacity = '1';
                    };
                    
                    img.src = image.full_url;
                    
                    const infoDiv = document.createElement('div');
                    infoDiv.className = 'p-2';
                    infoDiv.innerHTML = `
                        <small class="text-truncate d-block">${image.title || image.original_filename}</small>
                        <small class="text-muted">${image.formatted_size}</small>
                    `;
                    
                    imageDiv.appendChild(img);
                    imageDiv.appendChild(infoDiv);
                    col.appendChild(imageDiv);
                    fragment.appendChild(col);
                });
            }
            
            // ล้างและเพิ่มเนื้อหาใหม่ในครั้งเดียว
            grid.innerHTML = '';
            grid.appendChild(fragment);
        }

        function toggleImageSelection(imageId, imageUrl, imagePath) {
            const item = event.currentTarget;
            
            if (currentPickerMultiple) {
                // Handle multiple selection
                const existingIndex = selectedImages.findIndex(img => img.id === imageId);
                if (existingIndex > -1) {
                    selectedImages.splice(existingIndex, 1);
                    item.classList.remove('selected');
                } else {
                    selectedImages.push({ id: imageId, url: imageUrl, path: imagePath });
                    item.classList.add('selected');
                }
            } else {
                // Handle single selection
                document.querySelectorAll('.image-picker-item').forEach(el => el.classList.remove('selected'));
                selectedImages = [{ id: imageId, url: imageUrl, path: imagePath }];
                item.classList.add('selected');
            }
            
            updateTestStatus(`เลือกรูปภาพแล้ว: ${imagePath}`, 'info');
        }

        function selectImage() {
            if (selectedImages.length === 0) {
                alert('กรุณาเลือกรูปภาพ');
                return;
            }
            
            const image = selectedImages[0];
            const hiddenInput = document.getElementById(currentPickerField + '_hidden');
            hiddenInput.value = image.path;
            
            // Update preview
            const previewDiv = document.getElementById('selectedImagePreview');
            const previewImg = document.getElementById('previewImage');
            previewImg.src = image.url;
            previewDiv.style.display = 'block';
            
            updateTestStatus(`เลือกรูปภาพสำเร็จ: ${image.path}`, 'success');
            
            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('imagePickerModal')).hide();
        }

        function searchImages() {
            // Clear previous timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
            
            // Debounce search to prevent too many requests
            searchTimeout = setTimeout(() => {
                loadImages();
            }, 300);
        }

        function updateTestStatus(message, type) {
            const statusDiv = document.getElementById('testStatus');
            const iconClass = {
                'success': 'fas fa-check-circle text-success',
                'warning': 'fas fa-exclamation-triangle text-warning',
                'info': 'fas fa-info-circle text-info',
                'error': 'fas fa-times-circle text-danger'
            };
            
            statusDiv.innerHTML = `<p><i class="${iconClass[type]}"></i> ${message}</p>`;
        }

        // Add event listeners when document is ready
        document.addEventListener('DOMContentLoaded', function() {
            updateTestStatus('ระบบพร้อมใช้งาน', 'success');
            
            // Auto search when typing
            const searchInput = document.getElementById('imageSearch');
            if (searchInput) {
                searchInput.addEventListener('input', searchImages);
            }
            
            // Auto search when category changes
            const categoryFilter = document.getElementById('categoryFilter');
            if (categoryFilter) {
                categoryFilter.addEventListener('change', searchImages);
            }
        });
    </script>
</body>
</html>
