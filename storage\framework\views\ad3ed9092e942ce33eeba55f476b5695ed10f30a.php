<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'name' => 'image',
    'label' => 'เลือกรูปภาพ',
    'value' => null,
    'category' => 'general',
    'required' => false,
    'multiple' => false
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'name' => 'image',
    'label' => 'เลือกรูปภาพ',
    'value' => null,
    'category' => 'general',
    'required' => false,
    'multiple' => false
]); ?>
<?php foreach (array_filter(([
    'name' => 'image',
    'label' => 'เลือกรูปภาพ',
    'value' => null,
    'category' => 'general',
    'required' => false,
    'multiple' => false
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div class="mb-3">
    <label for="<?php echo e($name); ?>" class="form-label">
        <?php echo e($label); ?>

        <?php if($required): ?>
            <span class="text-danger">*</span>
        <?php endif; ?>
    </label>
    
    <div class="image-picker-container">
        <!-- Current Image Preview -->
        <?php if($value): ?>
            <div class="current-image-preview mb-3">
                <div class="position-relative d-inline-block">
                    <img src="<?php echo e(Storage::disk('public')->url($value)); ?>" 
                         alt="Current image" 
                         class="img-thumbnail"
                         style="max-width: 200px; max-height: 150px;">
                    <button type="button" 
                            class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1"
                            onclick="removeCurrentImage('<?php echo e($name); ?>')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <input type="hidden" name="<?php echo e($name); ?>" value="<?php echo e($value); ?>" id="<?php echo e($name); ?>_hidden">
            </div>
        <?php endif; ?>

        <!-- Image Picker Buttons -->
        <div class="btn-group" role="group">
            <button type="button" 
                    class="btn btn-outline-primary" 
                    onclick="openImagePicker('<?php echo e($name); ?>', '<?php echo e($category); ?>', <?php echo e($multiple ? 'true' : 'false'); ?>)">
                <i class="fas fa-images me-2"></i>เลือกจาก Gallery
            </button>
            <button type="button" 
                    class="btn btn-outline-secondary" 
                    onclick="openFileUpload('<?php echo e($name); ?>')">
                <i class="fas fa-upload me-2"></i>อัปโหลดใหม่
            </button>
        </div>

        <!-- Hidden File Input -->
        <input type="file" 
               id="<?php echo e($name); ?>_file" 
               class="d-none" 
               accept="image/*"
               <?php echo e($multiple ? 'multiple' : ''); ?>

               onchange="handleFileUpload('<?php echo e($name); ?>', this)">

        <!-- Hidden Input for Selected Image -->
        <?php if(!$value): ?>
            <input type="hidden" name="<?php echo e($name); ?>" id="<?php echo e($name); ?>_hidden">
        <?php endif; ?>
    </div>
</div>

<!-- Image Picker Modal -->
<div class="modal fade" id="imagePickerModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">เลือกรูปภาพ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Search and Filter -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <input type="text" 
                               id="imageSearch" 
                               class="form-control" 
                               placeholder="ค้นหารูปภาพ...">
                    </div>
                    <div class="col-md-4">
                        <select id="categoryFilter" class="form-select">
                            <option value="">ทุกหมวดหมู่</option>
                            <option value="general">ทั่วไป</option>
                            <option value="menu">เมนูอาหาร</option>
                            <option value="news">ข่าวสาร</option>
                            <option value="hero">หน้าแรก/สไลด์</option>
                            <option value="restaurant">ข้อมูลร้าน</option>
                            <option value="about">เกี่ยวกับเรา</option>
                            <option value="contact">ติดต่อเรา</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-primary w-100" onclick="searchImages()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- Images Container -->
                <div class="position-relative">
                    <!-- Images Grid -->
                    <div id="imagesGrid" class="row g-3" style="min-height: 300px;">
                        <!-- Images will be loaded here -->
                    </div>

                    <!-- Loading -->
                    <div id="imagesLoading" class="text-center py-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">กำลังโหลด...</span>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">กำลังโหลดรูปภาพ...</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                <button type="button" class="btn btn-primary" onclick="selectImage()">เลือก</button>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.image-picker-item {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
    position: relative;
}

.image-picker-item:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.image-picker-item.selected {
    border-color: #0d6efd;
    background-color: #e7f3ff;
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

.image-picker-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    transition: opacity 0.3s ease;
}

/* Loading animation */
#imagesGrid {
    transition: opacity 0.3s ease;
    min-height: 300px;
}

#imagesLoading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.modal-body {
    position: relative;
    min-height: 400px;
}

/* Image placeholder */
.image-placeholder {
    background: linear-gradient(90deg, #f8f9fa 25%, #e9ecef 50%, #f8f9fa 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Smooth grid transitions */
.row.g-3 {
    transition: opacity 0.3s ease;
}

/* Image item animations */
.image-picker-item {
    animation: fadeInUp 0.3s ease forwards;
    opacity: 0;
    transform: translateY(20px);
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Staggered animation for grid items */
.col-md-3:nth-child(1) .image-picker-item { animation-delay: 0.05s; }
.col-md-3:nth-child(2) .image-picker-item { animation-delay: 0.1s; }
.col-md-3:nth-child(3) .image-picker-item { animation-delay: 0.15s; }
.col-md-3:nth-child(4) .image-picker-item { animation-delay: 0.2s; }
.col-md-3:nth-child(5) .image-picker-item { animation-delay: 0.25s; }
.col-md-3:nth-child(6) .image-picker-item { animation-delay: 0.3s; }
.col-md-3:nth-child(7) .image-picker-item { animation-delay: 0.35s; }
.col-md-3:nth-child(8) .image-picker-item { animation-delay: 0.4s; }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
let currentPickerField = null;
let currentPickerMultiple = false;
let selectedImages = [];

function openImagePicker(fieldName, category = 'general', multiple = false) {
    currentPickerField = fieldName;
    currentPickerMultiple = multiple;
    selectedImages = [];

    // Reset modal state
    const grid = document.getElementById('imagesGrid');
    const loading = document.getElementById('imagesLoading');
    const searchInput = document.getElementById('imageSearch');

    // แสดง placeholder ใน grid แทนการล้าง
    grid.innerHTML = `
        <div class="col-12 text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">กำลังเตรียมข้อมูล...</span>
            </div>
            <div class="mt-2">
                <small class="text-muted">กำลังเตรียม Gallery...</small>
            </div>
        </div>
    `;
    grid.style.opacity = '1';
    grid.style.pointerEvents = 'auto';
    loading.style.display = 'none';
    searchInput.value = '';

    // Set category filter
    document.getElementById('categoryFilter').value = category;

    // Show modal first
    const modal = new bootstrap.Modal(document.getElementById('imagePickerModal'));
    modal.show();

    // Load images after modal animation completes
    setTimeout(() => {
        loadImages();
    }, 300);
}

function openFileUpload(fieldName) {
    document.getElementById(fieldName + '_file').click();
}

function handleFileUpload(fieldName, input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Create FormData for upload
        const formData = new FormData();
        formData.append('image', file);
        formData.append('category', 'general'); // Default category
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

        // Show loading state
        const container = document.querySelector(`[id="${fieldName}_file"]`).closest('.image-picker-container');
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'text-center py-2';
        loadingDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">กำลังอัปโหลด...</span></div> กำลังอัปโหลด...';
        container.appendChild(loadingDiv);

        // Upload file
        fetch('<?php echo e(route('admin.images.upload')); ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loadingDiv.remove();
            if (data.success) {
                updateImagePreview(fieldName, data.data.url, data.data.path);
            } else {
                alert('เกิดข้อผิดพลาดในการอัปโหลด: ' + (data.message || 'ไม่ทราบสาเหตุ'));
            }
        })
        .catch(error => {
            loadingDiv.remove();
            console.error('Upload error:', error);
            alert('เกิดข้อผิดพลาดในการอัปโหลด');
        });

        // Clear input
        input.value = '';
    }
}

function removeCurrentImage(fieldName) {
    document.querySelector(`input[name="${fieldName}"]`).value = '';
    document.querySelector('.current-image-preview').remove();
}

function loadImages() {
    const loading = document.getElementById('imagesLoading');
    const grid = document.getElementById('imagesGrid');

    // แสดง loading โดยไม่ซ่อน grid
    loading.style.display = 'block';
    loading.style.position = 'absolute';
    loading.style.top = '50%';
    loading.style.left = '50%';
    loading.style.transform = 'translate(-50%, -50%)';
    loading.style.zIndex = '1000';
    loading.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
    loading.style.padding = '20px';
    loading.style.borderRadius = '8px';
    loading.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';

    const category = document.getElementById('categoryFilter').value;
    const search = document.getElementById('imageSearch').value;

    const params = new URLSearchParams();
    if (category) params.append('category', category);
    if (search) params.append('search', search);

    fetch(`<?php echo e(route('admin.images.api')); ?>?${params}`)
        .then(response => response.json())
        .then(data => {
            // ซ่อน loading
            loading.style.display = 'none';

            if (data.success) {
                renderImagesSmooth(data.data);
            } else {
                renderImagesSmooth([]);
            }
        })
        .catch(error => {
            console.error('Error loading images:', error);
            loading.style.display = 'none';
            renderImagesSmooth([]);
        });
}

function renderImages(images) {
    const grid = document.getElementById('imagesGrid');

    // ใช้ DocumentFragment เพื่อลดการ reflow
    const fragment = document.createDocumentFragment();

    if (images.length === 0) {
        const emptyCol = document.createElement('div');
        emptyCol.className = 'col-12 text-center py-4';
        emptyCol.innerHTML = '<p class="text-muted">ไม่พบรูปภาพ</p>';
        fragment.appendChild(emptyCol);
    } else {
        images.forEach(image => {
            const col = document.createElement('div');
            col.className = 'col-md-3 col-sm-4 col-6';

            // สร้าง image element แยกเพื่อจัดการ loading
            const imageDiv = document.createElement('div');
            imageDiv.className = 'image-picker-item';
            imageDiv.onclick = () => toggleImageSelection(image.id, image.full_url, image.path);

            const img = document.createElement('img');
            img.alt = image.title || image.original_filename;
            img.style.opacity = '0';
            img.style.transition = 'opacity 0.3s ease';

            // จัดการ loading ของรูปภาพ
            img.onload = function() {
                this.style.opacity = '1';
            };

            img.onerror = function() {
                this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuC4hOC4suC4meC5guC4q+C4peC4lOC4o+C4ueC4m+C4oOC4suC4nTwvdGV4dD48L3N2Zz4=';
                this.style.opacity = '1';
            };

            img.src = image.full_url;

            const infoDiv = document.createElement('div');
            infoDiv.className = 'p-2';
            infoDiv.innerHTML = `
                <small class="text-truncate d-block">${image.title || image.original_filename}</small>
                <small class="text-muted">${image.formatted_size}</small>
            `;

            imageDiv.appendChild(img);
            imageDiv.appendChild(infoDiv);
            col.appendChild(imageDiv);
            fragment.appendChild(col);
        });
    }

    // ล้างและเพิ่มเนื้อหาใหม่ในครั้งเดียว
    grid.innerHTML = '';
    grid.appendChild(fragment);
}

function renderImagesSmooth(images) {
    const grid = document.getElementById('imagesGrid');

    // สร้าง grid ใหม่แบบซ่อนไว้
    const newGrid = document.createElement('div');
    newGrid.className = 'row g-3';
    newGrid.style.opacity = '0';
    newGrid.style.transition = 'opacity 0.3s ease';

    // ใช้ DocumentFragment เพื่อลดการ reflow
    const fragment = document.createDocumentFragment();

    if (images.length === 0) {
        const emptyCol = document.createElement('div');
        emptyCol.className = 'col-12 text-center py-4';
        emptyCol.innerHTML = '<p class="text-muted">ไม่พบรูปภาพ</p>';
        fragment.appendChild(emptyCol);
    } else {
        images.forEach((image, index) => {
            const col = document.createElement('div');
            col.className = 'col-md-3 col-sm-4 col-6';
            col.style.animationDelay = `${index * 0.05}s`;

            // สร้าง image element แยกเพื่อจัดการ loading
            const imageDiv = document.createElement('div');
            imageDiv.className = 'image-picker-item';
            imageDiv.onclick = () => toggleImageSelection(image.id, image.full_url, image.path);

            // สร้าง placeholder ก่อน
            const placeholder = document.createElement('div');
            placeholder.className = 'image-placeholder';
            placeholder.style.width = '100%';
            placeholder.style.height = '120px';
            placeholder.style.backgroundColor = '#f8f9fa';
            placeholder.style.display = 'flex';
            placeholder.style.alignItems = 'center';
            placeholder.style.justifyContent = 'center';
            placeholder.style.borderRadius = '4px';
            placeholder.innerHTML = '<div class="spinner-border spinner-border-sm text-muted" role="status"></div>';

            const img = document.createElement('img');
            img.alt = image.title || image.original_filename;
            img.style.width = '100%';
            img.style.height = '120px';
            img.style.objectFit = 'cover';
            img.style.display = 'none';
            img.style.transition = 'opacity 0.3s ease';

            // จัดการ loading ของรูปภาพ
            img.onload = function() {
                placeholder.style.display = 'none';
                this.style.display = 'block';
                this.style.opacity = '1';
            };

            img.onerror = function() {
                placeholder.innerHTML = '<small class="text-muted">ไม่สามารถโหลดรูปได้</small>';
            };

            // เริ่มโหลดรูปภาพ
            img.src = image.full_url;

            const infoDiv = document.createElement('div');
            infoDiv.className = 'p-2';
            infoDiv.innerHTML = `
                <small class="text-truncate d-block">${image.title || image.original_filename}</small>
                <small class="text-muted">${image.formatted_size}</small>
            `;

            imageDiv.appendChild(placeholder);
            imageDiv.appendChild(img);
            imageDiv.appendChild(infoDiv);
            col.appendChild(imageDiv);
            fragment.appendChild(col);
        });
    }

    // เพิ่มเนื้อหาใน newGrid
    newGrid.appendChild(fragment);

    // แทนที่ grid เดิมด้วย newGrid
    const parent = grid.parentNode;
    parent.insertBefore(newGrid, grid);

    // แสดง newGrid และลบ grid เดิม
    setTimeout(() => {
        newGrid.style.opacity = '1';
        setTimeout(() => {
            if (grid.parentNode) {
                grid.parentNode.removeChild(grid);
            }
            newGrid.id = 'imagesGrid';
        }, 300);
    }, 50);
}

function toggleImageSelection(imageId, imageUrl, imagePath) {
    const item = event.currentTarget;
    
    if (currentPickerMultiple) {
        // Multiple selection logic
        if (item.classList.contains('selected')) {
            item.classList.remove('selected');
            selectedImages = selectedImages.filter(img => img.id !== imageId);
        } else {
            item.classList.add('selected');
            selectedImages.push({ id: imageId, url: imageUrl, path: imagePath });
        }
    } else {
        // Single selection logic
        document.querySelectorAll('.image-picker-item').forEach(el => el.classList.remove('selected'));
        item.classList.add('selected');
        selectedImages = [{ id: imageId, url: imageUrl, path: imagePath }];
    }
}

function selectImage() {
    if (selectedImages.length === 0) {
        alert('กรุณาเลือกรูปภาพ');
        return;
    }
    
    if (currentPickerMultiple) {
        // Handle multiple selection
        console.log('Multiple images selected:', selectedImages);
    } else {
        // Handle single selection
        const image = selectedImages[0];
        const hiddenInput = document.getElementById(currentPickerField + '_hidden');
        hiddenInput.value = image.path;
        
        // Update preview
        updateImagePreview(currentPickerField, image.url, image.path);
    }
    
    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('imagePickerModal')).hide();
}

function updateImagePreview(fieldName, imageUrl, imagePath) {
    const container = document.querySelector(`input[name="${fieldName}"]`).closest('.image-picker-container');
    
    // Remove existing preview
    const existingPreview = container.querySelector('.current-image-preview');
    if (existingPreview) {
        existingPreview.remove();
    }
    
    // Create new preview
    const preview = document.createElement('div');
    preview.className = 'current-image-preview mb-3';
    preview.innerHTML = `
        <div class="position-relative d-inline-block">
            <img src="${imageUrl}" 
                 alt="Selected image" 
                 class="img-thumbnail"
                 style="max-width: 200px; max-height: 150px;">
            <button type="button" 
                    class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1"
                    onclick="removeCurrentImage('${fieldName}')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <input type="hidden" name="${fieldName}" value="${imagePath}" id="${fieldName}_hidden">
    `;
    
    // Insert preview before buttons
    const buttons = container.querySelector('.btn-group');
    container.insertBefore(preview, buttons);
}

let searchTimeout;

function searchImages() {
    // Clear previous timeout
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }

    // Debounce search to prevent too many requests
    searchTimeout = setTimeout(() => {
        loadImages();
    }, 300);
}

// Add event listeners when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Auto search when typing
    const searchInput = document.getElementById('imageSearch');
    if (searchInput) {
        searchInput.addEventListener('input', searchImages);
    }

    // Auto search when category changes
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        categoryFilter.addEventListener('change', searchImages);
    }
});

// Auto-search on input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('imageSearch');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(searchImages, 500);
        });
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\LastNoodletest\resources\views/components/image-picker.blade.php ENDPATH**/ ?>