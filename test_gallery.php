<?php
require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

echo "=== ทดสอบระบบ Gallery ===\n";

// ตรวจสอบโฟลเดอร์รูปภาพ
$storagePath = storage_path('app/public');
$publicStoragePath = public_path('storage');

echo "Storage Path: {$storagePath}\n";
echo "Public Storage Path: {$publicStoragePath}\n";
echo "Storage Link exists: " . (File::exists($publicStoragePath) ? 'Yes' : 'No') . "\n";

// ตรวจสอบโฟลเดอร์ต่างๆ
$folders = ['menu-items', 'categories', 'restaurant', 'about-page', 'contact-page', 'gallery'];

foreach ($folders as $folder) {
    $folderPath = $storagePath . '/' . $folder;
    $exists = File::exists($folderPath);
    $count = 0;

    if ($exists) {
        $files = File::files($folderPath);
        $count = count($files);
    }

    echo "- {$folder}: " . ($exists ? "มี ({$count} ไฟล์)" : "ไม่มี") . "\n";
}

echo "\n=== ทดสอบ API Endpoint ===\n";

// ทดสอบ API endpoint
$baseUrl = 'http://localhost:8000';
$apiUrl = $baseUrl . '/admin/images/api';

echo "ทดสอบ API: {$apiUrl}\n";

// ใช้ curl เพื่อทดสอบ API
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Status: {$httpCode}\n";

if ($response) {
    $data = json_decode($response, true);
    if ($data && isset($data['success'])) {
        echo "API Response: " . ($data['success'] ? 'สำเร็จ' : 'ล้มเหลว') . "\n";
        if ($data['success'] && isset($data['data'])) {
            echo "จำนวนรูปภาพจาก API: " . count($data['data']) . "\n";
        }
    } else {
        echo "API Response ไม่ถูกต้อง\n";
    }
} else {
    echo "ไม่สามารถเชื่อมต่อ API ได้\n";
}

echo "\n=== เสร็จสิ้น ===\n";
