<?php
// Simple test page for Gallery system
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Gallery - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <meta name="csrf-token" content="test-token">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">ทดสอบระบบ Gallery - Admin</h1>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            หน้านี้ใช้สำหรับทดสอบระบบ Gallery ที่ปรับปรุงแล้ว โดยจะเชื่อมต่อกับ API จริงของระบบ
        </div>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>ทดสอบ Image Picker Component</h5>
                    </div>
                    <div class="card-body">
                        <!-- Test Image Picker 1 -->
                        <div class="mb-4">
                            <label class="form-label">รูปภาพเมนู:</label>
                            <div class="image-picker-container">
                                <div class="btn-group" role="group">
                                    <button type="button" 
                                            class="btn btn-outline-primary" 
                                            onclick="openImagePicker('menu_image', 'menu', false)">
                                        <i class="fas fa-images me-2"></i>เลือกจาก Gallery
                                    </button>
                                    <button type="button" 
                                            class="btn btn-outline-secondary" 
                                            onclick="alert('ฟีเจอร์อัปโหลดใหม่')">
                                        <i class="fas fa-upload me-2"></i>อัปโหลดใหม่
                                    </button>
                                </div>
                                <input type="hidden" name="menu_image" id="menu_image_hidden">
                            </div>
                        </div>
                        
                        <!-- Test Image Picker 2 -->
                        <div class="mb-4">
                            <label class="form-label">รูปภาพร้าน:</label>
                            <div class="image-picker-container">
                                <div class="btn-group" role="group">
                                    <button type="button" 
                                            class="btn btn-outline-primary" 
                                            onclick="openImagePicker('restaurant_image', 'restaurant', false)">
                                        <i class="fas fa-images me-2"></i>เลือกจาก Gallery
                                    </button>
                                    <button type="button" 
                                            class="btn btn-outline-secondary" 
                                            onclick="alert('ฟีเจอร์อัปโหลดใหม่')">
                                        <i class="fas fa-upload me-2"></i>อัปโหลดใหม่
                                    </button>
                                </div>
                                <input type="hidden" name="restaurant_image" id="restaurant_image_hidden">
                            </div>
                        </div>
                        
                        <!-- Test Image Picker 3 -->
                        <div class="mb-4">
                            <label class="form-label">รูปภาพทั่วไป:</label>
                            <div class="image-picker-container">
                                <div class="btn-group" role="group">
                                    <button type="button" 
                                            class="btn btn-outline-primary" 
                                            onclick="openImagePicker('general_image', 'general', false)">
                                        <i class="fas fa-images me-2"></i>เลือกจาก Gallery
                                    </button>
                                    <button type="button" 
                                            class="btn btn-outline-secondary" 
                                            onclick="alert('ฟีเจอร์อัปโหลดใหม่')">
                                        <i class="fas fa-upload me-2"></i>อัปโหลดใหม่
                                    </button>
                                </div>
                                <input type="hidden" name="general_image" id="general_image_hidden">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>สถานะการทดสอบ</h5>
                    </div>
                    <div class="card-body">
                        <div id="testStatus">
                            <p><i class="fas fa-clock text-warning"></i> รอการทดสอบ...</p>
                        </div>
                        
                        <hr>
                        
                        <h6>ข้อมูลที่เลือก:</h6>
                        <div id="selectedData">
                            <small class="text-muted">ยังไม่มีข้อมูล</small>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>การทดสอบ API</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-info btn-sm" onclick="testAPI()">
                            <i class="fas fa-flask"></i> ทดสอบ API
                        </button>
                        <div id="apiTestResult" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Picker Modal -->
    <div class="modal fade" id="imagePickerModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">เลือกรูปภาพ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <input type="text"
                                   id="imageSearch"
                                   class="form-control"
                                   placeholder="ค้นหารูปภาพ...">
                        </div>
                        <div class="col-md-4">
                            <select id="categoryFilter" class="form-select">
                                <option value="">ทุกหมวดหมู่</option>
                                <option value="general">ทั่วไป</option>
                                <option value="menu">เมนูอาหาร</option>
                                <option value="news">ข่าวสาร</option>
                                <option value="hero">หน้าแรก/สไลด์</option>
                                <option value="restaurant">ข้อมูลร้าน</option>
                                <option value="about">เกี่ยวกับเรา</option>
                                <option value="contact">ติดต่อเรา</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-primary w-100" onclick="searchImages()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Images Container -->
                    <div class="position-relative">
                        <!-- Images Grid -->
                        <div id="imagesGrid" class="row g-3" style="min-height: 300px;">
                            <!-- Images will be loaded here -->
                        </div>

                        <!-- Loading -->
                        <div id="imagesLoading" class="text-center py-4" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">กำลังโหลด...</span>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">กำลังโหลดรูปภาพ...</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <button type="button" class="btn btn-primary" onclick="selectImage()">เลือก</button>
                </div>
            </div>
        </div>
    </div>

    <style>
        .image-picker-item {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            border-radius: 8px;
            overflow: hidden;
            background: #f8f9fa;
            position: relative;
        }

        .image-picker-item:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .image-picker-item.selected {
            border-color: #0d6efd;
            background-color: #e7f3ff;
            box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
        }

        .image-picker-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            transition: opacity 0.3s ease;
        }

        #imagesGrid {
            transition: opacity 0.3s ease;
        }

        #imagesLoading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }

        .modal-body {
            position: relative;
            min-height: 300px;
        }

        .image-picker-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            z-index: 1;
        }

        .image-picker-item img[style*="opacity: 1"] + div,
        .image-picker-item img[style*="opacity: 1"] {
            z-index: 2;
            position: relative;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateTestStatus(message, type) {
            const statusDiv = document.getElementById('testStatus');
            const iconClass = {
                'success': 'fas fa-check-circle text-success',
                'warning': 'fas fa-exclamation-triangle text-warning',
                'info': 'fas fa-info-circle text-info',
                'error': 'fas fa-times-circle text-danger'
            };
            
            statusDiv.innerHTML = `<p><i class="${iconClass[type]}"></i> ${message}</p>`;
        }
        
        function updateSelectedData() {
            const selectedDiv = document.getElementById('selectedData');
            const menuImage = document.getElementById('menu_image_hidden').value;
            const restaurantImage = document.getElementById('restaurant_image_hidden').value;
            const generalImage = document.getElementById('general_image_hidden').value;
            
            let html = '';
            if (menuImage) html += `<small><strong>เมนู:</strong> ${menuImage}</small><br>`;
            if (restaurantImage) html += `<small><strong>ร้าน:</strong> ${restaurantImage}</small><br>`;
            if (generalImage) html += `<small><strong>ทั่วไป:</strong> ${generalImage}</small><br>`;
            
            selectedDiv.innerHTML = html || '<small class="text-muted">ยังไม่มีข้อมูล</small>';
        }
        
        function testAPI() {
            const resultDiv = document.getElementById('apiTestResult');
            resultDiv.innerHTML = '<small class="text-info"><i class="fas fa-spinner fa-spin"></i> กำลังทดสอบ...</small>';
            
            fetch('/admin/images/api')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        resultDiv.innerHTML = `<small class="text-success"><i class="fas fa-check"></i> API ทำงานปกติ (${data.data.length} รูปภาพ)</small>`;
                    } else {
                        resultDiv.innerHTML = '<small class="text-warning"><i class="fas fa-exclamation"></i> API ส่งคืนข้อผิดพลาด</small>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `<small class="text-danger"><i class="fas fa-times"></i> API ไม่ทำงาน: ${error.message}</small>`;
                });
        }
        
        // Override selectImage function to update our test data
        const originalSelectImage = window.selectImage;
        window.selectImage = function() {
            if (originalSelectImage) {
                originalSelectImage();
            }
            setTimeout(updateSelectedData, 100);
        };
        
        // Gallery JavaScript Functions
        let currentPickerField = null;
        let currentPickerMultiple = false;
        let selectedImages = [];
        let searchTimeout;

        function openImagePicker(fieldName, category = 'general', multiple = false) {
            currentPickerField = fieldName;
            currentPickerMultiple = multiple;
            selectedImages = [];

            // Reset modal state
            const grid = document.getElementById('imagesGrid');
            const loading = document.getElementById('imagesLoading');
            const searchInput = document.getElementById('imageSearch');

            // Clear previous state
            grid.innerHTML = '';
            grid.style.opacity = '1';
            grid.style.pointerEvents = 'auto';
            loading.style.display = 'none';
            searchInput.value = '';

            // Set category filter
            document.getElementById('categoryFilter').value = category;

            // Show modal first
            const modal = new bootstrap.Modal(document.getElementById('imagePickerModal'));
            modal.show();

            updateTestStatus(`เปิด Gallery Modal สำหรับ ${fieldName} (${category})`, 'info');

            // Load images after modal is shown
            setTimeout(() => {
                loadImages();
            }, 100);
        }

        function loadImages() {
            const loading = document.getElementById('imagesLoading');
            const grid = document.getElementById('imagesGrid');

            updateTestStatus('กำลังโหลดรูปภาพ...', 'warning');

            // แสดง loading และซ่อน grid ชั่วคราว
            loading.style.display = 'block';
            grid.style.opacity = '0.3';
            grid.style.pointerEvents = 'none';

            const category = document.getElementById('categoryFilter').value;
            const search = document.getElementById('imageSearch').value;

            const params = new URLSearchParams();
            if (category) params.append('category', category);
            if (search) params.append('search', search);

            fetch('/admin/images/api?' + params.toString())
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    loading.style.display = 'none';
                    grid.style.opacity = '1';
                    grid.style.pointerEvents = 'auto';

                    if (data.success) {
                        renderImages(data.data);
                        updateTestStatus(`โหลดรูปภาพสำเร็จ (${data.data.length} รูป)`, 'success');
                    } else {
                        throw new Error('API returned error');
                    }
                })
                .catch(error => {
                    loading.style.display = 'none';
                    grid.style.opacity = '1';
                    grid.style.pointerEvents = 'auto';

                    updateTestStatus(`ไม่สามารถโหลดรูปภาพได้: ${error.message}`, 'error');
                    renderImages([]);
                });
        }

        function renderImages(images) {
            const grid = document.getElementById('imagesGrid');

            // ใช้ DocumentFragment เพื่อลดการ reflow
            const fragment = document.createDocumentFragment();

            if (images.length === 0) {
                const emptyCol = document.createElement('div');
                emptyCol.className = 'col-12 text-center py-4';
                emptyCol.innerHTML = '<p class="text-muted">ไม่พบรูปภาพ</p>';
                fragment.appendChild(emptyCol);
            } else {
                images.forEach(image => {
                    const col = document.createElement('div');
                    col.className = 'col-md-3 col-sm-4 col-6';

                    // สร้าง image element แยกเพื่อจัดการ loading
                    const imageDiv = document.createElement('div');
                    imageDiv.className = 'image-picker-item';
                    imageDiv.onclick = () => toggleImageSelection(image.id, image.full_url, image.path);

                    const img = document.createElement('img');
                    img.alt = image.title || image.original_filename;
                    img.style.opacity = '0';
                    img.style.transition = 'opacity 0.3s ease';

                    // จัดการ loading ของรูปภาพ
                    img.onload = function() {
                        this.style.opacity = '1';
                    };

                    img.onerror = function() {
                        this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuC4hOC4suC4meC5guC4q+C4leC4lOC4o+C4ueC4m+C4oOC4suC4nTwvdGV4dD48L3N2Zz4=';
                        this.style.opacity = '1';
                    };

                    img.src = image.full_url;

                    const infoDiv = document.createElement('div');
                    infoDiv.className = 'p-2';
                    infoDiv.innerHTML = `
                        <small class="text-truncate d-block">${image.title || image.original_filename}</small>
                        <small class="text-muted">${image.formatted_size}</small>
                    `;

                    imageDiv.appendChild(img);
                    imageDiv.appendChild(infoDiv);
                    col.appendChild(imageDiv);
                    fragment.appendChild(col);
                });
            }

            // ล้างและเพิ่มเนื้อหาใหม่ในครั้งเดียว
            grid.innerHTML = '';
            grid.appendChild(fragment);
        }

        function toggleImageSelection(imageId, imageUrl, imagePath) {
            const item = event.currentTarget;

            if (currentPickerMultiple) {
                // Handle multiple selection
                const existingIndex = selectedImages.findIndex(img => img.id === imageId);
                if (existingIndex > -1) {
                    selectedImages.splice(existingIndex, 1);
                    item.classList.remove('selected');
                } else {
                    selectedImages.push({ id: imageId, url: imageUrl, path: imagePath });
                    item.classList.add('selected');
                }
            } else {
                // Handle single selection
                document.querySelectorAll('.image-picker-item').forEach(el => el.classList.remove('selected'));
                selectedImages = [{ id: imageId, url: imageUrl, path: imagePath }];
                item.classList.add('selected');
            }

            updateTestStatus(`เลือกรูปภาพ: ${imagePath}`, 'info');
        }

        function selectImage() {
            if (selectedImages.length === 0) {
                alert('กรุณาเลือกรูปภาพ');
                return;
            }

            const image = selectedImages[0];
            const hiddenInput = document.getElementById(currentPickerField + '_hidden');
            hiddenInput.value = image.path;

            // Update preview
            updateImagePreview(currentPickerField, image.url, image.path);

            updateTestStatus(`เลือกรูปภาพสำเร็จ: ${image.path}`, 'success');
            updateSelectedData();

            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('imagePickerModal')).hide();
        }

        function updateImagePreview(fieldName, imageUrl, imagePath) {
            const container = document.querySelector(`input[name="${fieldName}"]`).closest('.image-picker-container');

            // Remove existing preview
            const existingPreview = container.querySelector('.current-image-preview');
            if (existingPreview) {
                existingPreview.remove();
            }

            // Create new preview
            const preview = document.createElement('div');
            preview.className = 'current-image-preview mb-3';
            preview.innerHTML = `
                <div class="position-relative d-inline-block">
                    <img src="${imageUrl}"
                         alt="Selected image"
                         class="img-thumbnail"
                         style="max-width: 200px; max-height: 150px;">
                    <button type="button"
                            class="btn btn-sm btn-danger position-absolute top-0 end-0 m-1"
                            onclick="removeCurrentImage('${fieldName}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <input type="hidden" name="${fieldName}" value="${imagePath}" id="${fieldName}_hidden">
            `;

            // Insert preview before buttons
            const buttons = container.querySelector('.btn-group');
            container.insertBefore(preview, buttons);
        }

        function removeCurrentImage(fieldName) {
            const container = document.querySelector(`input[name="${fieldName}"]`).closest('.image-picker-container');
            const preview = container.querySelector('.current-image-preview');
            if (preview) {
                preview.remove();
            }

            const hiddenInput = document.getElementById(fieldName + '_hidden');
            hiddenInput.value = '';

            updateSelectedData();
            updateTestStatus(`ลบรูปภาพ ${fieldName} แล้ว`, 'info');
        }

        function searchImages() {
            // Clear previous timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // Debounce search to prevent too many requests
            searchTimeout = setTimeout(() => {
                loadImages();
            }, 300);
        }

        document.addEventListener('DOMContentLoaded', function() {
            updateTestStatus('ระบบพร้อมใช้งาน', 'success');
            testAPI(); // Auto test API on load

            // Auto search when typing
            const searchInput = document.getElementById('imageSearch');
            if (searchInput) {
                searchInput.addEventListener('input', searchImages);
            }

            // Auto search when category changes
            const categoryFilter = document.getElementById('categoryFilter');
            if (categoryFilter) {
                categoryFilter.addEventListener('change', searchImages);
            }
        });
    </script>
</body>
</html>
